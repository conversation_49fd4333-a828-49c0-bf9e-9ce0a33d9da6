{"2211.05075v1": {"title": "Supporting AI/ML Security Workers through an Adversarial Techniques, Tools, and Common Knowledge (AI/ML ATT&CK) Framework", "authors": ["<PERSON><PERSON>ad <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper focuses on supporting AI/ML Security Workers -- professionals\ninvolved in the development and deployment of secure AI-enabled software\nsystems. It presents AI/ML Adversarial Techniques, Tools, and Common Knowledge\n(AI/ML ATT&CK) framework to enable AI/ML Security Workers intuitively to\nexplore offensive and defensive tactics.", "pdf_url": "http://arxiv.org/pdf/2211.05075v1", "published": "2022-11-09"}, "2403.15481v2": {"title": "Navigating Fairness: Practitioners' Understanding, Challenges, and Strategies in AI/ML Development", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise in the use of AI/ML applications across industries has sparked more\ndiscussions about the fairness of AI/ML in recent times. While prior research\non the fairness of AI/ML exists, there is a lack of empirical studies focused\non understanding the perspectives and experiences of AI practitioners in\ndeveloping a fair AI/ML system. Understanding AI practitioners' perspectives\nand experiences on the fairness of AI/ML systems are important because they are\ndirectly involved in its development and deployment and their insights can\noffer valuable real-world perspectives on the challenges associated with\nensuring fairness in AI/ML systems. We conducted semi-structured interviews\nwith 22 AI practitioners to investigate their understanding of what a 'fair\nAI/ML' is, the challenges they face in developing a fair AI/ML system, the\nconsequences of developing an unfair AI/ML system, and the strategies they\nemploy to ensure AI/ML system fairness. We developed a framework showcasing the\nrelationship between AI practitioners' understanding of 'fair AI/ML' system and\n(i) their challenges in its development, (ii) the consequences of developing an\nunfair AI/ML system, and (iii) strategies used to ensure AI/ML system fairness.\nBy exploring AI practitioners' perspectives and experiences, this study\nprovides actionable insights to enhance AI/ML fairness, which may promote\nfairer systems, reduce bias, and foster public trust in AI technologies.\nAdditionally, we also identify areas for further investigation and offer\nrecommendations to aid AI practitioners and AI companies in navigating\nfairness.", "pdf_url": "http://arxiv.org/pdf/2403.15481v2", "published": "2024-03-21"}, "2204.12641v1": {"title": "Framework for disruptive AI/ML Innovation", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This framework enables C suite executive leaders to define a business plan\nand manage technological dependencies for building AI/ML Solutions. The\nbusiness plan of this framework provides components and background information\nto define strategy and analyze cost. Furthermore, the business plan represents\nthe fundamentals of AI/ML Innovation and AI/ML Solutions. Therefore, the\nframework provides a menu for managing and investing in AI/ML. Finally, this\nframework is constructed with an interdisciplinary and holistic view of AI/ML\nInnovation and builds on advances in business strategy in harmony with\ntechnological progress for AI/ML. This framework incorporates value chain,\nsupply chain, and ecosystem strategies.", "pdf_url": "http://arxiv.org/pdf/2204.12641v1", "published": "2022-04-27"}, "2407.08895v1": {"title": "What do AI/ML practitioners think about AI/ML bias?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "AI leaders and companies have much to offer to AI/ML practitioners to support\nthem in addressing and mitigating biases in the AI/ML systems they develop.\nAI/ML practitioners need to receive the necessary resources and support from\nexperts to develop unbiased AI/ML systems. However, our studies have revealed a\ndiscrepancy between practitioners' understanding of 'AI/ML bias' and the\ndefinitions of tech companies and researchers. This indicates a misalignment\nthat needs addressing. Efforts should be made to match practitioners'\nunderstanding of AI/ML bias with the definitions developed by tech companies\nand researchers. These efforts could yield a significant return on investment\nby aiding AI/ML practitioners in developing unbiased AI/ML systems.", "pdf_url": "http://arxiv.org/pdf/2407.08895v1", "published": "2024-07-11"}, "2209.08873v1": {"title": "New Trends in Photonic Switching and Optical Network Architecture for Data Centre and Computing Systems", "authors": ["S. <PERSON><PERSON>"], "summary": "AI/ML for data centres and data centres for AI/ML are defining new trends in\ncloud computing. Disaggregated heterogeneous reconfigurable computing systems\nrealized by photonic interconnects and photonic switching expect greatly\nenhanced throughput and energy-efficiency for AI/ML workloads, especially when\naided by an AI/ML control plane.", "pdf_url": "http://arxiv.org/pdf/2209.08873v1", "published": "2022-09-19"}}