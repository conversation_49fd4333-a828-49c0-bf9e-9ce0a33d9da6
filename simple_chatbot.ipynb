! pip install -r requirements.txt

import arxiv
import json
import os
from typing import List
from dotenv import load_dotenv
import ollama
import traceback

PAPER_DIR = "papers"


def search_papers(topic: str, max_results: int = 5) -> List[str]:
    """
    Search for papers on arXiv based on a topic and store their information.
    """
    print(f"\n[Tool Call] search_papers(topic='{topic}', max_results={max_results})")
    client = arxiv.Client()
    search = arxiv.Search(
        query=topic,
        max_results=max_results,
        sort_by=arxiv.SortCriterion.Relevance
    )
    papers = client.results(search)

    path = os.path.join(PAPER_DIR, topic.lower().replace(" ", "_"))
    os.makedirs(path, exist_ok=True)
    file_path = os.path.join(path, "papers_info.json")

    try:
        with open(file_path, "r") as json_file:
            papers_info = json.load(json_file)
    except (FileNotFoundError, json.JSONDecodeError):
        papers_info = {}

    paper_ids = []
    for paper in papers:
        paper_id = paper.get_short_id()
        paper_ids.append(paper_id)
        paper_info = {
            'title': paper.title,
            'authors': [author.name for author in paper.authors],
            'summary': paper.summary,
            'pdf_url': paper.pdf_url,
            'published': str(paper.published.date())
        }
        papers_info[paper_id] = paper_info

    with open(file_path, "w") as json_file:
        json.dump(papers_info, json_file, indent=2)

    result_message = f"Found {len(paper_ids)} papers. Results are saved in: {file_path}"
    print(f"[Tool Result] {result_message}")
    return paper_ids


search_papers("AI and Ml")

def extract_info(paper_id: str) -> str:
    """
    Search for information about a specific paper across all topic directories.
    """
    print(f"\n[Tool Call] extract_info(paper_id='{paper_id}')")
    for item in os.listdir(PAPER_DIR):
        item_path = os.path.join(PAPER_DIR, item)
        if os.path.isdir(item_path):
            file_path = os.path.join(item_path, "papers_info.json")
            if os.path.isfile(file_path):
                try:
                    with open(file_path, "r") as json_file:
                        papers_info = json.load(json_file)
                        if paper_id in papers_info:
                            result = json.dumps(papers_info[paper_id], indent=2)
                            print(f"[Tool Result] Found info for {paper_id}")
                            return result
                except (FileNotFoundError, json.JSONDecodeError) as e:
                    print(f"Error reading {file_path}: {str(e)}")
                    continue
    not_found_message = f"There's no saved information related to paper {paper_id}."
    print(f"[Tool Result] {not_found_message}")
    return not_found_message


extract_info('2211.05075v1')

tools_schema = [
    {
        "type": "function",
        "function": {
            "name": "search_papers",
            "description": "Search for papers on arXiv based on a topic and store their information.",
            "parameters": {
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "The topic to search for"
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum number of results to retrieve (default: 5 in function code)"
                    }
                },
                "required": ["topic"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "extract_info",
            "description": "Search for information about a specific paper across all topic directories.",
            "parameters": {
                "type": "object",
                "properties": {
                    "paper_id": {
                        "type": "string",
                        "description": "The ID of the paper to look for"
                    }
                },
                "required": ["paper_id"]
            }
        }
    }
]


# --- Tool Mapping and Execution ---
mapping_tool_function = {
    "search_papers": search_papers,
    "extract_info": extract_info
}

def execute_tool(tool_name: str, tool_args: dict) -> str:
    """Executes a tool and returns its stringified result."""
    try:
        function_to_call = mapping_tool_function[tool_name]
        result = function_to_call(**tool_args)

        if result is None:
            return "The operation completed but didn't return any specific results."
        elif isinstance(result, list):
            return ', '.join(map(str, result)) # Ensure all list items are strings
        elif isinstance(result, dict):
            return json.dumps(result, indent=2)
        else:
            return str(result)
    except Exception as e:
        print(f"Error during tool execution '{tool_name}': {e}")
        traceback.print_exc()
        return f"Error: Tool '{tool_name}' execution failed. Details: {str(e)}"


load_dotenv()
ollama_client = ollama.Client()
OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "llama3.2:3b") # Default to llama3 if not set


"""# --- Query Processing Function ---
def process_user_query(user_query: str):
    """
    Processes a user query, interacts with Ollama, and handles tool calls.
    """
    messages = [{'role': 'user', 'content': user_query}]
    print(f"\n--- Using Ollama model: {OLLAMA_MODEL} ---")

    while True: # Loop to handle sequential tool calls if needed
        print(f"\n> Sending to Ollama: User query or tool results.")
        # print(f"DEBUG: Messages sent: {json.dumps(messages, indent=2)}") # Uncomment for debugging

        try:
            response = ollama_client.chat(
                model=OLLAMA_MODEL,
                messages=messages,
                tools=tools_schema
            )
        except Exception as e:
            print(f"!!! Ollama API Error: {e}")
            traceback.print_exc()
            return # Exit processing this query on API error

        # print(f"DEBUG: Response received: {json.dumps(response, indent=2)}") # Uncomment for debugging

        assistant_message = response.get('message', {})
        messages.append(assistant_message)

        if assistant_message.get('content'):
            print(f"\nAssistant: {assistant_message['content']}")

        if not assistant_message.get('tool_calls'):
            break # No more tool calls, assistant's turn is finished

        tool_calls = assistant_message['tool_calls']
        tool_response_messages = []

        for tool_call in tool_calls:
            tool_name = tool_call.get('function', {}).get('name')
            tool_call_id = tool_call.get('id') # Ollama provides this
            
            if not tool_name or not tool_call_id:
                print(f"!!! Invalid tool_call structure: {tool_call}")
                tool_response_messages.append({
                    'role': 'tool',
                    'tool_call_id': tool_call_id or "unknown_id", 
                    'name': tool_name or "unknown_tool",
                    'content': "Error: Invalid tool call structure from LLM."
                })
                continue

            print(f"\nLLM requests tool: '{tool_name}' (Call ID: {tool_call_id})")
            
            raw_args = tool_call.get('function', {}).get('arguments', '{}')
            print(f"Arguments (raw): {raw_args}")

            try:
                tool_args = json.loads(raw_args) if isinstance(raw_args, str) else raw_args
                if not isinstance(tool_args, dict): # Ensure args are a dict for ** unpacking
                    raise ValueError("Tool arguments must be a JSON object (dict).")
            except (json.JSONDecodeError, ValueError) as e:
                print(f"!!! Error decoding arguments for tool '{tool_name}': {e}")
                error_content = f"Error: Could not parse arguments for {tool_name}. Invalid JSON or format. Arguments received: {raw_args}. Error: {e}"
                tool_response_messages.append({
                    'role': 'tool',
                    'tool_call_id': tool_call_id,
                    'name': tool_name,
                    'content': error_content
                })
                continue # Skip to next tool call

            tool_result_content = execute_tool(tool_name, tool_args)
            
            tool_response_messages.append({
                'role': 'tool',
                'tool_call_id': tool_call_id, # Crucial for Ollama to match response to call
                'name': tool_name, # Ollama also expects the tool name in the response
                'content': tool_result_content
            })
        
        messages.extend(tool_response_messages)
        # Loop continues: send tool results back to Ollama
"""

# --- Query Processing Function ---
def process_user_query(user_query: str):
    """
    Processes a user query, interacts with Ollama, and handles tool calls.
    """
    messages = [{'role': 'user', 'content': user_query}]
    print(f"\n--- Using Ollama model: {OLLAMA_MODEL} ---")

    while True: # Loop to handle sequential tool calls if needed
        print(f"\n> Sending to Ollama: User query or tool results.")
        # print(f"DEBUG: Messages sent: {json.dumps(messages, indent=2)}")

        try:
            response = ollama_client.chat(
                model=OLLAMA_MODEL,
                messages=messages,
                tools=tools_schema
            )
        except Exception as e:
            print(f"!!! Ollama API Error: {e}")
            traceback.print_exc()
            return

        # print(f"DEBUG: Response received: {json.dumps(response, indent=2)}")

        assistant_message = response.get('message', {})
        messages.append(assistant_message)

        if assistant_message.get('content'):
            print(f"\nAssistant: {assistant_message['content']}")

        if not assistant_message.get('tool_calls'):
            break

        raw_tool_calls = assistant_message['tool_calls'] # This is a list
        tool_response_messages_for_ollama = []

        for i, tool_call_item in enumerate(raw_tool_calls):
            # The ollama library typically returns tool_call_item as a dict:
            # {'id': 'call_xyz', 'type': 'function', 'function': {'name': 'func_name', 'arguments': {'arg1': 'val1'}}}
            # We need to ensure we are accessing it correctly.

            # Defensive checks:
            if not isinstance(tool_call_item, dict):
                print(f"!!! Unexpected tool_call_item type: {type(tool_call_item)}. Item: {tool_call_item}")
                # Try to create a somewhat meaningful error message for Ollama
                error_content = f"Error: Received unexpected tool call item format from LLM. Item: {str(tool_call_item)}"
                tool_response_messages_for_ollama.append({
                    'role': 'tool',
                    # Try to generate a unique ID if none is available
                    'tool_call_id': f"malformed_call_{i}", 
                    'name': "unknown_tool",
                    'content': error_content
                })
                continue

            tool_call_id = tool_call_item.get('id')
            function_details = tool_call_item.get('function') # This should be a dict

            if not tool_call_id or not isinstance(function_details, dict):
                print(f"!!! Malformed tool_call (missing 'id' or 'function' is not a dict): {tool_call_item}")
                error_content = f"Error: Malformed tool call received from LLM (missing id or valid function details). Call: {str(tool_call_item)}"
                tool_response_messages_for_ollama.append({
                    'role': 'tool',
                    'tool_call_id': tool_call_id or f"malformed_id_{i}",
                    'name': function_details.get('name', "unknown_tool") if isinstance(function_details, dict) else "unknown_tool",
                    'content': error_content
                })
                continue

            tool_name = function_details.get('name')
            tool_args_dict = function_details.get('arguments') # Should be a dict

            if not tool_name:
                print(f"!!! Malformed tool_call (missing function name in function_details): {tool_call_item}")
                tool_response_messages_for_ollama.append({
                    'role': 'tool',
                    'tool_call_id': tool_call_id,
                    'name': "unknown_tool",
                    'content': "Error: Tool call from LLM missing function name."
                })
                continue
            
            print(f"\nLLM requests tool: '{tool_name}' (Call ID: {tool_call_id})")
            
            if not isinstance(tool_args_dict, dict):
                error_content = (f"Error: Tool arguments for '{tool_name}' are not a dictionary as expected. "
                                 f"Received type: {type(tool_args_dict)}, Value: {tool_args_dict}")
                print(f"!!! {error_content}")
                tool_response_messages_for_ollama.append({
                    'role': 'tool',
                    'tool_call_id': tool_call_id,
                    'name': tool_name,
                    'content': error_content
                })
                continue
            
            print(f"Arguments (dict): {tool_args_dict}")
            tool_result_content = execute_tool(tool_name, tool_args_dict)
            
            tool_response_messages_for_ollama.append({
                'role': 'tool',
                'tool_call_id': tool_call_id,
                'name': tool_name,
                'content': tool_result_content
            })
        
        if tool_response_messages_for_ollama: # Only extend if there are actual tool responses
            messages.extend(tool_response_messages_for_ollama)
        # Loop continues

if __name__ == "__main__":
    chat_loop()

